using System;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using GoTrack.Msisdns;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Domain.Services;
using Volo.Abp.Timing;

namespace GoTrack.Totps;

/// <summary>
/// This is an OTP implementation based on the standard RFC6238TOTP and heavily inspired by microsoft's https://github.com/aspnet/AspNetIdentity/blob/main/src/Microsoft.AspNet.Identity.Core/Rfc6238AuthenticationService.cs
/// </summary>
public class Rfc6238TotpManager : ITotpManager, IDomainService
{
    //When needed options relating to token generation timestep and format or length can be moved to options
    private static readonly DateTime _unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
    private static readonly TimeSpan _timestep = TimeSpan.FromMinutes(GoTrackConsts.Rfc6238AuthenticationServiceTimeStepMinutes);
    private static readonly Encoding _encoding = new UTF8Encoding(false, true);
    public const string StaticNonSyrianOtp = "733094";

    private readonly IClock _clock;
    private readonly ILogger<Rfc6238TotpManager> _logger;

    public Rfc6238TotpManager(IClock clock, ILogger<Rfc6238TotpManager> logger)
    {
        _clock = clock;
        _logger = logger;
    }
    
    public string GenerateCode(Msisdn phoneNumber)
    {
        // Check if this is a test number in the range +999 0000 0 00 xx
        if (IsTestNumber(phoneNumber.ToString()))
        {
            var staticOtp = GenerateStaticOtp(phoneNumber.ToString());
            _logger.LogTrace("Generated static OTP {0} for test number {1}", staticOtp, phoneNumber.ToString());
            return staticOtp;
        }

        var securityToken = new SecurityToken(phoneNumber);
        //TODO data protection modifier
        string modifier = null;
        // Allow a variance of no greater than 9 minutes in either direction
        var currentTimeStep = GetCurrentTimeStepNumber();
        using (var hashAlgorithm = new HMACSHA1(securityToken.GetDataNoClone()))
        {

            int otpDigits = ComputeTotp(hashAlgorithm, currentTimeStep, modifier);
            var otpString = otpDigits.ToString("D6");
            _logger.LogTrace("Generated {0} Pertaining to {1} ", otpString ,phoneNumber.ToString());
            return otpString;
        }
    }
    
    public bool IsValidCode(Msisdn phoneNumber, string otpCode)
    {
        int code = 0;
        if (!Int32.TryParse(otpCode, out code))
        {
            return false;
        }

        // Check if this is a test number in the range +999 0000 0 00 xx
        if (IsTestNumber(phoneNumber.ToString()))
        {
            var expectedStaticOtp = GenerateStaticOtp(phoneNumber.ToString());
            return otpCode == expectedStaticOtp;
        }

        var securityToken = new SecurityToken(phoneNumber);
        //TODO data protection modifier
        string modifier = null;

        // Allow a variance of no greater than 9 minutes in either direction
        var currentTimeStep = GetCurrentTimeStepNumber();
        using (var hashAlgorithm = new HMACSHA1(securityToken.GetDataNoClone()))
        {
            for (var i = -2; i <= 2; i++)
            {
                var computedTotp = ComputeTotp(hashAlgorithm, (ulong) ((long) currentTimeStep + i), modifier);
                if (computedTotp == code)
                {
                    return true;
                }
            }
        }

        // No match
        return false;
    }

    public void ValidateCode(Msisdn phoneNumber, string otpCode)
    {
        // Check if this is a test number in the range +999 0000 0 00 xx
        if (IsTestNumber(phoneNumber.ToString()))
        {
            var expectedStaticOtp = GenerateStaticOtp(phoneNumber.ToString());
            if (otpCode != expectedStaticOtp)
            {
                throw new BusinessException(GoTrackDomainErrorCodes.OtpInvalid);
            }
            return;
        }

        if (IsSyrianNumber(phoneNumber.ToString()) is false)
        {
            if (otpCode != StaticNonSyrianOtp)
            {
                throw new BusinessException(GoTrackDomainErrorCodes.OtpInvalid);
            }

            return;
        }

        if (!IsValidCode(phoneNumber, otpCode))
            throw new BusinessException(GoTrackDomainErrorCodes.OtpInvalid);
    }

    /// <summary>
    /// Checks if the phone number is in the test range +999 0000 0 00 xx (where xx is 00-99)
    /// Supports formats: ***********xx and 999000000xx
    /// </summary>
    private bool IsTestNumber(string msisdn)
    {
        // Format: ***********xx (14 digits)
        if (msisdn.Length == 14 && msisdn.StartsWith("***********"))
        {
            var lastTwoDigits = msisdn.Substring(11, 2);
            return int.TryParse(lastTwoDigits, out var value) && value >= 0 && value <= 99;
        }

        // Format: 999000000xx (12 digits)
        if (msisdn.Length == 12 && msisdn.StartsWith("999000000"))
        {
            var lastTwoDigits = msisdn.Substring(9, 2);
            return int.TryParse(lastTwoDigits, out var value) && value >= 0 && value <= 99;
        }

        return false;
    }

    /// <summary>
    /// Generates a deterministic 6-digit OTP from the last two digits of a test phone number.
    /// Uses an obscure formula to make the OTP less predictable.
    /// </summary>
    private string GenerateStaticOtp(string msisdn)
    {
        if (!IsTestNumber(msisdn))
        {
            throw new ArgumentException("Phone number is not in the test range", nameof(msisdn));
        }

        // Extract the last two digits (xx) from the phone number
        int x;
        if (msisdn.Length == 14 && msisdn.StartsWith("***********"))
        {
            x = int.Parse(msisdn.Substring(11, 2));
        }
        else if (msisdn.Length == 12 && msisdn.StartsWith("999000000"))
        {
            x = int.Parse(msisdn.Substring(9, 2));
        }
        else
        {
            throw new ArgumentException("Invalid test phone number format", nameof(msisdn));
        }

        // Apply the obscure formula:
        // 1. Multiply by a constant (10101) to create a repeating pattern.
        // 2. Use the modulo operator to keep the number within a 6-digit range.
        // 3. Add the original number back to obscure the pattern slightly.
        long obscureIntermediate = (long)x * 10101;
        long otpValue = (obscureIntermediate % 999999) + x;

        // Ensure the result is always 6 digits, padding with leading zeros if necessary
        return otpValue.ToString("D6");
    }

    private bool IsSyrianNumber(string msisdn)
    {
        return (msisdn.Length == 12 && msisdn.StartsWith("9639")) ||
               (msisdn.Length == 14 && msisdn.StartsWith("009639"));
    }
    
    private static int ComputeTotp(HashAlgorithm hashAlgorithm, ulong timestepNumber, string modifier)
    {
        // # of 0's = length of pin
        const int mod = 1000000;

        // See https://tools.ietf.org/html/rfc4226
        // We can add an optional modifier
        var timestepAsBytes = BitConverter.GetBytes(IPAddress.HostToNetworkOrder((long) timestepNumber));
        var hash = hashAlgorithm.ComputeHash(ApplyModifier(timestepAsBytes, modifier));

        // Generate DT string
        var offset = hash[hash.Length - 1] & 0xf;
        // Debug.Assert(offset + 4 < hash.Length);
        var binaryCode = (hash[offset] & 0x7f) << 24
                         | (hash[offset + 1] & 0xff) << 16
                         | (hash[offset + 2] & 0xff) << 8
                         | (hash[offset + 3] & 0xff);
        
        return binaryCode%mod;
    }
    
    private static byte[] ApplyModifier(byte[] input, string modifier)
    {
        if (String.IsNullOrEmpty(modifier))
        {
            return input;
        }

        var modifierBytes = _encoding.GetBytes(modifier);
        var combined = new byte[checked(input.Length + modifierBytes.Length)];
        Buffer.BlockCopy(input, 0, combined, 0, input.Length);
        Buffer.BlockCopy(modifierBytes, 0, combined, input.Length, modifierBytes.Length);
        return combined;
    }
    // More info: https://tools.ietf.org/html/rfc6238#section-4
    
    private ulong GetCurrentTimeStepNumber()
    {
        var delta = _clock.Now.ToUniversalTime() - _unixEpoch;
        return (ulong) (delta.Ticks/_timestep.Ticks);
    }
    
    internal  sealed class SecurityToken
    {
    
        private readonly byte[] _data;

        public SecurityToken(Msisdn msisdn)
        {
            _data = (byte[]) msisdn.ToString().GetBytes().Clone();
        }
        public SecurityToken(byte[] data)
        {
            _data = (byte[]) data.Clone();
        }

        internal byte[] GetDataNoClone()
        {
            return _data;
        }
    
    }
}